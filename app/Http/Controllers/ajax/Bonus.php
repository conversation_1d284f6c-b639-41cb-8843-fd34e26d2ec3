<?php

namespace App\Http\Controllers\ajax;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\BonusSettingHelper;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\BonusHelper;
use App\Services\pattern\PointRecords;
use App\Services\pattern\TaskHelper;

class Bonus extends Controller
{
    /*處理月分紅再分配*/
    public function auto_month_distribution(Request $request)
    {
        /*撈取範圍為當前月份的前一月*/
        $date = date('Y-m-01');
        // $date = ('2024-12-01');
        $pre_month_s = strtotime($date . ' -1month');
        $pre_month_e = strtotime($date);
        dump('撈取開始時間：' . $pre_month_s);
        dump('撈取結束時間：' . $pre_month_e);
        // exit;

        /*撈取指定月份還尚未處理分配的點數*/
        $all_num = Db::connection('main_db')->table('dividend_month_record')
            ->where('datetime', '>=', $pre_month_s)
            ->where('datetime', '<', $pre_month_e)
            ->where('used', '=', 0)
            ->sum('num');
        dump('可分配點數：' . $all_num);

        $user_obj = []; /*月分紅會用到的資格與加權資料整理，內容是 $user_id => $user_obj_item*/
        $user_obj_item = [
            'partner' => 0,                         /*前一個月直推會員成為合夥人的數量*/
            'product' => (float)0,                  /*前一個月自己的消費商品CV金額*/
            'product_down' => (float)0,             /*前一個月下線的消費商品CV金額*/
            'point_increasable' => (float)0,        /*前一個月底自己持有的增值積分數*/
            'point_increasable_down' => (float)0,   /*前一個月底下線持有的增值積分數*/
        ];

        /*計算前一個月會員直接推薦新註冊的會員有成為合夥人的數量*/
        $accounts = Db::connection('main_db')->select( //取得新加入會員，是直推，且是合夥人
            "SELECT id, upline_user
                                                        FROM `account`
                                                        WHERE createtime>= ?
                                                        AND createtime< ?
                                                        AND registration_from=1
                                                        AND partner_level_id>0
                                                        AND upline_user!=0",
            [
                $pre_month_s,
                $pre_month_e
            ]
        );
        // dump($accounts);
        // 從符合資的的新會員資料中，把推薦者的資料整理成user_obj
        foreach ($accounts as $key => $value) {
            $upline_user_id = $value->upline_user;
            if (!isset($user_obj[$upline_user_id])) {
                $user_obj[$upline_user_id] = json_decode(json_encode($user_obj_item), true);
            }
            $user_obj[$upline_user_id]['partner'] += 1; //累積推薦了多少合夥人
        }
        // dump($user_obj);

        /*計算前一個月會員的消費CV金額*/
        /*先找出會員id對推薦會員id的紀錄(無紀錄表示無推薦者)*/
        $userid_to_upline = Db::connection('main_db')->table('account')->where('upline_user', '!=', 0)->pluck('upline_user', 'id');

        /*撈取前一個月消費*/
        $result = $this->get_orderform_product_cv($pre_month_s, $pre_month_e);
        $orderform_id_to_user = $result['orderform_id_to_user'];
        $products = $result['products'];
        foreach ($products as $value) {
            /*累計購買者自己的CV消費*/
            $user_id = $orderform_id_to_user[$value->orderform_id];
            if (!isset($user_obj[$user_id])) {
                $user_obj[$user_id] = json_decode(json_encode($user_obj_item), true);
            }
            $user_obj[$user_id]['product'] += $value->sum_num;

            /*累計購買者的推薦者的CV消費*/
            $upline_user_id = $userid_to_upline[$user_id] ?? 0;
            if ($upline_user_id) {
                if (!isset($user_obj[$upline_user_id])) {
                    $user_obj[$upline_user_id] = json_decode(json_encode($user_obj_item), true);
                }
                $user_obj[$upline_user_id]['product_down'] += $value->sum_num;
            }
        }
        // dump($user_obj);

        $bonus_setting = BonusSettingHelper::get_bonus_setting();
        $arr_vip_types = MemberInstance::get_vip_types([], true)['db_data'];
        $default_dividend_month_weighted = $arr_vip_types[array_keys($arr_vip_types)[0]]['dividend_month_weighted'] ?? 0; /*預設加成同最低級別*/
        $default_liability_gv = $arr_vip_types[array_keys($arr_vip_types)[0]]['liability_gv'] ?? 100;                     /*預設責任額同最低級別*/
        /*從user_obj篩選出符合資格者*/
        $qualified_user_ids = [-1];
        /*先找出會員id對應會員級別的紀錄(無紀錄表示無等級)*/
        $userid_to_viptype = Db::connection('main_db')->table('account')->where('vip_type', '!=', 0)->pluck('vip_type', 'id');
        // dump($userid_to_viptype);

        // @POINT 分紅資格判斷
        // foreach ($user_obj as $user_id => $user_item) {
        //     // 🔴 新增：必須持有增值積分
        //     if ($user_item['point_increasable'] <= 0) {
        //         continue; // 沒有增值積分直接跳過
        //     }

        //     // 🔴 修改：條件1改為個人購買132CV以上，條件2維持直推1位新合夥人
        //     if ($user_item['product'] >= 132 || $user_item['partner'] >= 1) {
        //         array_push($qualified_user_ids, $user_id);
        //     }
        // }
        foreach ($user_obj as $user_id => $user_item) {
            $gv_value = $arr_vip_types[$userid_to_viptype[$user_id] ?? -1]['liability_gv'] ?? $default_liability_gv;
            if (($user_item['product'] + $user_item['product_down']) >= $gv_value || $user_item['partner'] >= 1) {

                /* 個人消費CV金額 + 下線消費CV金額達VIP責任額 或 直推合夥人數量達1位*/
                /* 改成 個人持有積分 + 個人消費CV金額 > 132 或 直推合夥人數量達1位*/
                array_push($qualified_user_ids, $user_id);
            }
        }
        // dump($qualified_user_ids);

        /*計算前一個月所有會員持有的增值積分數量(排除系統跟月分紅帳號)*/
        $point_record =  Db::connection('main_db')->select(
            "SELECT user_id, sum(num) AS sum_num
                                                                FROM `point_increasable_record`
                                                                WHERE create_time< ?
                                                                GROUP BY user_id
                                                                HAVING sum_num > 0 AND user_id != ? AND user_id != ? ",
            [
                $pre_month_e,
                config('extra.skychakra.member_system'),
                config('extra.skychakra.member_month_divided'),
            ]
        );
        // dump($point_record);
        foreach ($point_record as $value) {
            if (!isset($user_obj[$value->user_id])) {
                $user_obj[$value->user_id] = json_decode(json_encode($user_obj_item), true);
            }
            $user_obj[$value->user_id]['point_increasable'] += $value->sum_num;
        }
        // dump($user_obj);

        /*計算符合資格者的下線的消費商品CV金額&下線持有的增值積分數*/
        /*為符合資格者找出他推薦的人*/
        $placeholders = implode(',', array_fill(0, count($qualified_user_ids), '?'));
        $down_line = Db::connection('main_db')->select(
            "SELECT id, number, name, upline_user
                                                            FROM `account`
                                                            WHERE registration_from=1
                                                            AND upline_user IN ($placeholders)",
            $qualified_user_ids
        );
        // dump($down_line);
        foreach ($down_line as $value) {
            $point_increasable = $user_obj[$value->id]['point_increasable'] ?? 0;
            if ($value->upline_user) {
                $user_obj[$value->upline_user]['point_increasable_down'] += $point_increasable;
            }
        }
        // dump($user_obj);

        /*整理符合資格者的資料，計算加權*/
        $weight_all = 0;
        $qualified_users = Db::connection('main_db')->table('account')
            ->select('id', 'vip_type')
            ->whereIn('id', $qualified_user_ids)
            ->get();
        $qualified_users = CommonService::objectToArray($qualified_users);
        foreach ($qualified_users as $key => $value) {
            $user_obj_one = $user_obj[$value['id']];

            /*基礎權重 = 前月月底自己持有的積分餘額 + 前月月底直接推廣會員的積分餘額*/
            $weight = $user_obj_one['point_increasable'] + $user_obj_one['point_increasable_down'];
            if ($weight < 0) {
                $weight = 0;
            } /*若加總小於0，則視為0*/
            // dump($value['id']);
            // dump($user_obj_one);

            /*乘以「會員級別」加權*/
            $type = $arr_vip_types[$value['vip_type']] ?? [];
            $dividend_month_weighted = $type['dividend_month_weighted'] ?? $default_dividend_month_weighted; /*無等級用預設加成*/
            $weight = $weight * $dividend_month_weighted;

            /*乘以「新推薦會員成為合夥人數」加權*/
            $weight = $weight * (1 + $user_obj_one['partner'] * $bonus_setting['month_weight_partner']);

            /*乘以「個人點數達幾個滿額」加權*/
            $gv_num = $user_obj_one['product'] + $user_obj_one['product_down'];
            $gv_times = floor($gv_num / $bonus_setting['month_weight_gv_num']);
            $weight = $weight * (1 + $gv_times * $bonus_setting['month_weight_gv']);

            $qualified_users[$key]['weight'] = $weight;
            /*累計總加乘*/
            $weight_all += $weight;
        }
        dump('總加權數：' . $weight_all);
        dump($qualified_users);

        if ($all_num == 0) {
            dump('此月無點數需再分配');
            return;
        }
        if ($weight_all == 0) {
            dump('此月無符合資格者可分配點數');
            return;
        }
        /*派送增值積分*/
        $msg = '月分紅(' . date('Y/m', $pre_month_s) . ')';
        $now_time = time();
        /*約分紅帳戶扣除增值積分*/
        $PointRecords = new PointRecords(config('extra.skychakra.member_month_divided'));
        $PointRecords->add_point_increasable_record([
            'num' => (float)$all_num * -1,
            'msg' => $msg,
            'create_time' => $now_time,
        ]);

        /*符合資格者依加權獲得增值積分*/
        foreach ($qualified_users as $key => $value) {
            $point_increasable = $all_num * $value['weight'] / $weight_all;
            if ($point_increasable == 0) {
                continue;
            }
            $PointRecords->change_user_id($value['id']);
            $PointRecords->add_point_increasable_record([
                'num' => (float)$point_increasable,
                'msg' => $msg,
                'create_time' => $now_time,
            ]);
        }

        /*標記月分紅紀錄為已使用*/
        Db::connection('main_db')->table('dividend_month_record')
            ->where('datetime', '>=', $pre_month_s)
            ->where('datetime', '<', $pre_month_e)
            ->where('used', '=', 0)
            ->update(['used' => 1]);
    }

    /*處理GV未達標*/
    public function auto_gv_check(Request $request)
    {
        $BonusHelper = new BonusHelper();
        $active_partner_ids = $BonusHelper->init_user_active_partner(0); /*取得有效合夥人的id們*/
        array_push($active_partner_ids, -1);

        $user_fail = []; /*紀錄會員對應連續未達標月數 [user_id=>個月是否達標的array紀錄(0.未達標, 1.達標)，ex:[0,0,1]]*/
        /*撈取無效合夥人 且有增值積分或消費圓滿點數或其他圓滿點數*/
        $partners_over = Db::connection('main_db')->table('account')
            ->select('id', 'vip_type')
            ->where('id', '!=',  config('extra.skychakra.member_system'))           /*非系統帳號*/
            ->where('id', '!=',  config('extra.skychakra.member_month_divided'))    /*非月分紅帳號*/
            ->whereNotIn('id', $active_partner_ids)                                 /*排除有效合夥人們*/
            ->where(function ($query) {
                $query->orWhere('point_increasable', '>', 0)
                    ->orWhere('increasing_limit_consumption', '>', 0)
                    ->orWhere('increasing_limit_other', '>', 0);
            })
            ->get();
        $partners_over = CommonService::objectToArray($partners_over);
        // dump($partners_over);exit;
        $partners_over_ids = [-1];
        foreach ($partners_over as $partner) {
            $user_fail[$partner['id']] = []; /*初始化GV檢查紀錄(預設空，後面依月份跑回圈時會逐月添加有無達標)*/
            array_push($partners_over_ids, $partner['id']);
        }
        // dump($partners_over_ids);

        /*撈取無效合夥人的下線*/
        $userid_to_upline = Db::connection('main_db')->table('account')
            ->select('id', 'upline_user')
            ->where('id', '!=',  config('extra.skychakra.member_system'))
            ->where('id', '!=',  config('extra.skychakra.member_month_divided'))
            ->whereIn('upline_user', $partners_over_ids)
            ->pluck('upline_user', 'id')
            ->toArray();
        // dump($userid_to_upline);


        $bonus_setting = BonusSettingHelper::get_bonus_setting();
        $arr_vip_types = MemberInstance::get_vip_types([], true)['db_data'];                        /*會員級別(id為key)*/
        $default_liability_gv = $arr_vip_types[array_keys($arr_vip_types)[0]]['liability_gv'] ?? 0; /*預設責任額同最低級別*/
        /*有兩種連續未達標月數要檢查，取較長的那一個做逐月檢查的迴圈*/
        $totatl_month = $bonus_setting['month_limit2zero'] > $bonus_setting['month_point2cash'] ? $bonus_setting['month_limit2zero'] : $bonus_setting['month_point2cash'];
        // dump($totatl_month);

        /*逐月逐會員檢查GV是否達標*/
        $date = date('Y-m-01');
        // $date = ('2024-12-01');
        $pre_month_s = strtotime($date . ' -1month');
        $pre_month_e = strtotime(date('Y-m-d', $pre_month_s) . ' +1month');
        foreach (array_fill(0, $totatl_month, 0) as $key => $value) {
            $pre_month_s = strtotime(date('Y-m-01', $pre_month_s) . ' -' . $key . 'month');
            $pre_month_e = strtotime(date('Y-m-d', $pre_month_s) . ' +1month');

            /*依照時間區間撈取商品*/
            $result = $this->get_orderform_product_cv($pre_month_s, $pre_month_e);
            $orderform_id_to_user = $result['orderform_id_to_user'];
            $products = $result['products'];
            // dump($result);

            /*依訂單商品統計會員GV值*/
            $user_gv = [];
            foreach ($products as $product) {
                $sum_num = $product->sum_num;
                $user_id = $orderform_id_to_user[$product->orderform_id];   /*本消費CV金額對應購買者*/
                $upline_user = $userid_to_upline[$user_id] ?? 0;            /*本消費CV金額對應購買者的推薦者(若購買者的推薦非無效和夥人，對應到0)*/

                if (!isset($user_gv[$user_id])) {
                    $user_gv[$user_id] = 0;
                }
                $user_gv[$user_id] += $sum_num;
                if (!isset($user_gv[$upline_user])) {
                    $user_gv[$upline_user] = 0;
                }
                $user_gv[$upline_user] += $sum_num;
            }
            // dump($user_gv);

            /*檢查GV是否達標*/
            foreach ($partners_over as $partner) {
                $gv_num = $user_gv[$partner['id']] ?? 0;
                $type = $arr_vip_types[$partner['vip_type']] ?? [];             /*取得會員套用的會員級別*/
                $liability_gv = $type['liability_gv'] ?? $default_liability_gv; /*依會員級別決定GV責任額，無等級時用預設責任額*/
                array_push($user_fail[$partner['id']], $liability_gv > $gv_num);  /*記錄此月未檢查狀況*/
            }
        }

        // dump($user_fail);exit;
        foreach ($user_fail as $user_id => $month_record) {
            $num_fail = $this->get_max_fail_months($month_record);

            $fail_msg = [];
            if ($num_fail >= $bonus_setting['month_point2cash']) { /*增值積分拋轉現金積分*/
                array_push($fail_msg, '連續未達標 ' . $bonus_setting['month_point2cash'] . ' 個月，處理「增值積分拋轉現金積分」');
                $BonusHelper->init_user_set($user_id);
                $BonusHelper->clean_point_increasable($user_id);
            }
            if ($num_fail >= $bonus_setting['month_limit2zero']) { /*消費圓滿點數清零*/
                array_push($fail_msg, '連續未達標 ' . $bonus_setting['month_limit2zero'] . ' 個月，處理「消費圓滿點數清零」');
                $BonusHelper->init_user_set($user_id);
                $BonusHelper->clean_increasing_limit_consumption_and_other($user_id);
            }
            if (count($fail_msg) > 0) {
                echo '會員(' . $user_id . '): ' . implode('、', $fail_msg) . '<br>';
            }
        }
        $BonusHelper->send_by_cal('個人責任額檢查');
    }

    /*根據指定日期撈出使用者的消費商品CV金額*/
    private function get_orderform_product_cv($time_s, $time_e)
    {
        $orderform_id_to_user = [];
        $orderforms = Db::connection('main_db')->select(
            //撈取指定時間區間有消費紀錄的id跟user_id
            "SELECT id, user_id
                                                        FROM `orderform`
                                                        WHERE do_award_time>= ?
                                                        AND do_award_time< ?",
            [
                $time_s,
                $time_e
            ]
        );
        // dump($orderforms);
        foreach ($orderforms as $value) {
            $orderform_id_to_user[$value->id] = $value->user_id;
        }

        $orderform_ids = array_map(function ($item) {
            return $item->id;
        }, $orderforms);
        array_push($orderform_ids, 0);
        $placeholders = implode(',', array_fill(0, count($orderform_ids), '?'));
        $products =  Db::connection('main_db')->select(
            "SELECT orderform_id, sum(`price_cv` - `deduct_invest` - `deduct_consumption`) AS sum_num
                                                        FROM `orderform_product`
                                                        WHERE orderform_id IN ($placeholders)
                                                        GROUP BY orderform_id
                                                        HAVING sum_num > 0",
            $orderform_ids
        );
        // dump($products);
        return [
            'orderform_id_to_user' => $orderform_id_to_user,
            'products' => $products,
        ];
    }

    /*根據紀錄的每月檢查GV狀況回傳最大連續失敗月數*/
    private function get_max_fail_months(array $month_record)
    {
        $max_count = 0;
        $current_count = 0;
        foreach ($month_record as $check) {
            if ($check) {
                $current_count++;
                $max_count = max($max_count, $current_count);
            } else {
                $current_count = 0; // 遇到 0 時重置計數
            }
        }
        return $max_count;
    }

    /*自動處理會員上傳獎勵*/
    public function auto_share_article_task()
    {
        $now_time = time();
        echo date('Y-m-d H:i:s', $now_time) . '<br>';
        if (date('H:i', $now_time) < '05:00' || date('H:i', $now_time) >= '05:3') {
            echo '超過執行時間' . '<br>';
        }
        TaskHelper::send_bonus_4();
        TaskHelper::send_bonus_5();
    }
}
