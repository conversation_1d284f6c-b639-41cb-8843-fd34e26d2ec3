<?php

namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
//photonicClass
use App\Services\CommonService;
use App\Services\pattern\BonusSettingHelper;
use App\Services\pattern\simpleFactory\orderFactory\OrderFactory;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\OrderHelper;
use App\Services\Order\RollbackPointbackService;
use App\Services\Order\DeleteOrderService;
use App\Http\Controllers\ajax\Ecpaylogistic;
use App\Services\pattern\MemberInstance;
use App\Http\Controllers\admin\Payfee;

class OrderCtrl extends MainController
{
    const RECEIPTS_STATE = ['未收款', '已收款'];
    const TRANSPORT_STATE = ['未出貨', '已出貨'];
    const ORDER_STATE = [
        'New' => '新進訂單',
        'Pickable' => '待包裝訂單',
        'Picked' => '可寄出訂單',
        'Complete' => '完成訂單',
        'Cancel' => '取消訂單',
        'Return' => '退貨訂單'
    ];
    protected $style;
    private $Order;
    private $tableName;
    private $coupon_tableName;

    public function __construct(
        Request $request,
        private RollbackPointbackService $rollbackService,
        private DeleteOrderService $deleteOrderService
    ) {
        parent::__construct();
        $this->tableName = 'orderform';
        $this->coupon_tableName = 'coupon_pool';

        $id = $request->post('id');
        if ($id != '' && $id != null) {
            $this->Order = OrderFactory::createOrder($id, $this->tableName, $this->coupon_tableName);
        }
    }

    /**
     * I訂單管理/訂單管理
     */
    public function index(Request $request)
    {
        $state = $request->get('state') ?? 'New';
        return $this->order_view($request, $state);
    }

    public function trash(Request $request)
    {
        $state = 'Trash';
        return $this->order_view($request, $state);
    }

    private function order_view(Request $request, $state)
    {
        $this->data['distributor_id_search'] = 0;

        /*付款方式下拉選*/
        $payments = [];
        $pay_fee_dict = Payfee::get_pay_fee_dict();
        $this->data['pay_fee_dict'] = $pay_fee_dict;
        foreach ($pay_fee_dict as $key => $value) {
            if ($value['online'] == 1) {
                array_push($payments, ['id' => $value['id'], 'name' => $value['name'], 'sys_status' => $value['sys_status']]);
            }
        }
        // dump($payments);exit;
        $this->data['payments'] = $payments;

        /*物流方式下拉選單*/
        $transports = DB::connection('main_db')->table($this->tableName)->select('transport')->groupBy('transport')->get();
        $this->data['transports'] = $transports;

        $this->data['state'] = $state;

        if ($state == 'Trash') {
            return view('order.order_ctrl.trash', ['data' => $this->data]);
        } else {
            return view('order.order_ctrl.order', ['data' => $this->data]);
        }
    }

    public function excel(Request $request)
    {
        $state = $request->get('state');
        $rowData = OrderHelper::get_orders($request, $this->admin_type, $this->my_distributor_id, $state, $need_page = false);
        // dump($rowData);exit;

        // 產生excel表頭
        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getActiveSheet()->setCellValue('A1', '下單日期');
        $objPHPExcel->getActiveSheet()->setCellValue('B1', '訂單編號');
        $objPHPExcel->getActiveSheet()->setCellValue('C1', '收件人姓名');
        $objPHPExcel->getActiveSheet()->setCellValue('D1', '收件人手機');
        $objPHPExcel->getActiveSheet()->setCellValue('E1', '收件人電話');
        $objPHPExcel->getActiveSheet()->setCellValue('F1', '收件人信箱');
        $objPHPExcel->getActiveSheet()->setCellValue('G1', '收件人地址');
        $objPHPExcel->getActiveSheet()->setCellValue('H1', '付款方式');
        $objPHPExcel->getActiveSheet()->setCellValue('I1', '匯款回報');
        $objPHPExcel->getActiveSheet()->setCellValue('J1', '收款狀況');
        $objPHPExcel->getActiveSheet()->setCellValue('K1', '運送方式');
        $objPHPExcel->getActiveSheet()->setCellValue('L1', '統一編號	');
        $objPHPExcel->getActiveSheet()->setCellValue('M1', '公司抬頭');
        $objPHPExcel->getActiveSheet()->setCellValue('N1', '收件人備註');
        $objPHPExcel->getActiveSheet()->setCellValue('O1', '出貨日期');
        $objPHPExcel->getActiveSheet()->setCellValue('P1', '商品名稱');
        $objPHPExcel->getActiveSheet()->setCellValue('Q1', '商品規格');
        $objPHPExcel->getActiveSheet()->setCellValue('R1', '商品單價');
        $objPHPExcel->getActiveSheet()->setCellValue('S1', '商品數量');
        $objPHPExcel->getActiveSheet()->setCellValue('T1', '訂單總金額');

        // 放入資料
        $row = 2;
        foreach ($rowData as $value) {
            $payment_name = Payfee::get_payment_name($value['payment']);
            $value['product'] = json_decode($value['product']);
            foreach ($value['product'] as $k_p => $v_p) {
                if (isset($v_p->info_id)) {
                    $objPHPExcel->getActiveSheet()->setCellValue('A' . $row, date('Y-m-d H:i', $value['create_time']));        // 下單日期
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . $row, $value['order_number']);                        // 訂單編號
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $row, $value['transport_location_name']);            // 收件人姓名
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $row, '="' . $value['transport_location_phone'] . '"');    // 收件人手機
                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $row, '="' . $value['transport_location_tele'] . '"');    // 收件人電話
                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $row, $value['transport_email']);                    // 收件人信箱
                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $row, $value['transport_location']);                    // 收件人地址
                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $row, $payment_name);                                // 付款方式
                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $row, $value['report']);                                // 匯款回報
                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $row, self::RECEIPTS_STATE[$value['receipts_state']]);        // 收款狀況
                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $row, $value['transport']);                            // 運送方式
                    $objPHPExcel->getActiveSheet()->setCellValue('L' . $row, $value['uniform_numbers']);                    // 統一編號
                    $objPHPExcel->getActiveSheet()->setCellValue('M' . $row, $value['company_title']);                        // 公司抬頭
                    $objPHPExcel->getActiveSheet()->setCellValue('N' . $row, $value['transport_location_textarea']);        // 收件人備註
                    $objPHPExcel->getActiveSheet()->setCellValue('O' . $row, $value['transport_date']);                        // 出貨日期

                    $names = explode('-', $v_p->name);
                    $p_name = count($names) > 1 ? implode('-', array_slice($names, 0, count($names) - 1)) : $v_p->name;
                    $p_type = count($names) > 1 ? end($names) : "";
                    $objPHPExcel->getActiveSheet()->setCellValue('P' . $row, $p_name);                                        // 商品名稱
                    $objPHPExcel->getActiveSheet()->setCellValue('Q' . $row, $p_type);                                        // 商品規格
                    $objPHPExcel->getActiveSheet()->setCellValue('R' . $row, $v_p->price);                                    // 商品單價
                    $objPHPExcel->getActiveSheet()->setCellValue('S' . $row, $v_p->num);                                    // 商品數量

                    $objPHPExcel->getActiveSheet()->setCellValue('T' . $row, $value['total']);                                // 訂單總金額
                    $row++;
                }
            }
        }

        // 下載檔案
        $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
        $filename = "訂單紀錄.xlsx";
        ob_end_clean();
        ob_start();
        header("Content-type: application/force-download");
        header("Content-Disposition: attachment; filename=\"$filename\"");
        $PHPExcelWriter->save('php://output');
    }
    public function group_excel(Request $request)
    {
        $state = $request->get('state');
        $distributors = [];
        $rowData = OrderHelper::get_orders($request, $this->admin_type, $this->my_distributor_id, $state, $need_page = false);

        foreach ($rowData as $key => $value) {
            $key = 'k_' . $value['distributor_id'];
            if (!isset($distributors[$key])) {
                if ($value['distributor_id'] == 0) {
                    $info = ['id' => $value['distributor_id'], 'name' => '平台'];
                } else {
                    /*會員資料*/
                    $MemberInstance = new MemberInstance($value['distributor_id']);
                    $info = $MemberInstance->get_user_data();
                }
                $distributors[$key] = ['info' => $info, 'total' => 0];
            }
            $distributors[$key]['total'] += $value['total'];
        }
        // dump($distributors);exit;

        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();;
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getActiveSheet()->setCellValue('A1', '名稱');
        $objPHPExcel->getActiveSheet()->setCellValue('B1', '訂單總額');

        // 放入資料
        $row = 2;
        foreach ($distributors as $value) {
            $objPHPExcel->getActiveSheet()->setCellValue('A' . $row, $value['info']['name']);    // 下單日期
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $row, $value['total']);            // 訂單編號
            $row++;
        }

        // 下載檔案
        $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
        $filename = "訂單金額總計.xlsx";
        ob_end_clean();
        ob_start();
        header("Content-type: application/force-download");
        header("Content-Disposition: attachment; filename=\"$filename\"");
        $PHPExcelWriter->save('php://output');
    }
    public function get_orderforms(Request $request)
    {
        $state = $request->get('state');
        $rowData_all = OrderHelper::get_orders($request, $this->admin_type, $this->my_distributor_id, $state, false);
        $data['total_orderform'] = $rowData_all;

        $rowData = OrderHelper::get_orders($request, $this->admin_type, $this->my_distributor_id, $state, true);
        $data['CurrentPage'] = $rowData->currentPage();
        $data['listRows'] = $rowData->perPage();
        $data['lastPage'] = $rowData->lastPage();

        $rowDataItem = $rowData->items();
        $rowDataItem = array_map(function ($value) {
            $value->create_date = date('Y-m-d', $value->create_time);
            $user = DB::connection('main_db')->table('account')->select('name', 'number')->find($value->user_id);
            $value->user_name = CommonService::objectToArray($user);
            $value->product = json_decode($value->product);
            if (isset(config('control.invoice_style_text')[$value->InvoiceStyle])) {
                $value->InvoiceStyleText = config('control.invoice_style_text')[$value->InvoiceStyle];
            } else {
                $value->InvoiceStyleText = config('control.invoice_style_text')[1];
            }
            $value->payment_name = DB::table('pay_fee')->select('name')->find($value->payment)->name ?? '';

            $value->product = array_map(function ($value) {
                return get_object_vars($value);
            }, $value->product);

            return $value;
        }, $rowDataItem);

        $data['rowDataItem'] = $rowDataItem;
        return $data;
    }

    /**
     * 匯入訂單
     */
    public function import(Request $request)
    {
        //接收檔案
        $files = $request->file("file");
        if (!$files) {
            $this->error("請上傳Excel檔");
        }
        $type = explode(".", $request->file("file")->getClientOriginalName());

        if (!$type[1] == 'xlsx') {
            $this->error("格式錯誤，請上傳Excel檔");
        }
        //儲存檔案
        $info = $files->move(ROOT_PATH . 'public' . DS . 'uploads' . DS . 'excel');
        //檔案路徑
        $filename = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'excel' . DS . $info->getFilename();

        //require_once 'Classes/PHPExcel/Reader/Excel5.php';
        $PHPReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader("Xlsx");
        $PHPExcel = $PHPReader->load($filename);
        $sheet = $PHPExcel->getSheet(0);
        $allRow = $sheet->getHighestRow(); //取得最大的行號
        $allColumn = $sheet->getHighestColumn(); //取得最大的列號

        $order_data = [];
        for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
            $data = [];

            $create_time = trim($PHPExcel->getActiveSheet()->getCell("A" . $currentRow)->getCalculatedValue()); /*訂單日期*/
            // $create_time = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($create_time)->format('Y-m-d H:i:s');
            $data['create_time'] = $create_time;
            $data['user_id'] = trim($PHPExcel->getActiveSheet()->getCell("B" . $currentRow)->getCalculatedValue()); /*消費者(會員編號)*/
            $data['transport_location_name'] = trim($PHPExcel->getActiveSheet()->getCell("C" . $currentRow)->getCalculatedValue());  /*消費者姓名*/
            $data['transport_email'] = trim($PHPExcel->getActiveSheet()->getCell("D" . $currentRow)->getCalculatedValue()); /*消費者email*/
            $data['transport_location_phone'] = trim($PHPExcel->getActiveSheet()->getCell("E" . $currentRow)->getCalculatedValue()); /*消費者手機*/
            $data['transport_location'] = trim($PHPExcel->getActiveSheet()->getCell("F" . $currentRow)->getCalculatedValue()); /*消費者地址*/
            $data['user_id_operation'] = trim($PHPExcel->getActiveSheet()->getCell("G" . $currentRow)->getCalculatedValue()); /*運營者(會員編號)*/
            $data['user_id_lecturer'] = trim($PHPExcel->getActiveSheet()->getCell("H" . $currentRow)->getCalculatedValue()); /*導師(會員編號)*/
            $data['user_id_center'] = trim($PHPExcel->getActiveSheet()->getCell("I" . $currentRow)->getCalculatedValue()); /*區域中心(會員編號)*/
            $data['currency'] = trim($PHPExcel->getActiveSheet()->getCell("J" . $currentRow)->getCalculatedValue()); /*幣別(設定訂單所屬「分站」用)*/
            $data['total'] = trim($PHPExcel->getActiveSheet()->getCell("K" . $currentRow)->getCalculatedValue()); /*訂單總金額(幣別依照「幣別」設定)*/
            $data['product'] = [ /*訂單商品*/
                [
                    'price' => $data['total'],
                    'num' => 1,
                    'price_supplier' => $this->getNumericCellValue($sheet, "L" . $currentRow), /*供應商結算金額(美金)*/
                    'price_cv' => $this->getNumericCellValue($sheet, "M" . $currentRow), /*CV值(美金)*/
                    'name' => trim($PHPExcel->getActiveSheet()->getCell("N" . $currentRow)->getCalculatedValue()), /*產品名稱*/
                    'distributor_id' => trim($PHPExcel->getActiveSheet()->getCell("O" . $currentRow)->getCalculatedValue()), /*供應商(會員編號)*/
                    'product_cate' => trim($PHPExcel->getActiveSheet()->getCell("P" . $currentRow)->getCalculatedValue()), /*商品類型(投資or消費)*/
                    'use_ad' => trim($PHPExcel->getActiveSheet()->getCell("Q" . $currentRow)->getCalculatedValue()), /*廣告(是or否，消費商品填入才有用)*/
                    'vip_type_reward' => trim($PHPExcel->getActiveSheet()->getCell("R" . $currentRow)->getCalculatedValue()), /*提升會員級別(對應線上「會員級別」設定)*/
                    'bonus_model_id' => trim($PHPExcel->getActiveSheet()->getCell("S" . $currentRow)->getCalculatedValue()), /*套用回饋模組(對應線上「回饋模組」設定)*/
                ],
            ];
            if (!$data['create_time'] && !$data['user_id']) {
                break;
            }
            array_push($order_data, $data);
        }

        $array_error_data = OrderHelper::importOrder($order_data);
        if (count($array_error_data) > 0) {
            foreach ($array_error_data as $key => $item) {
                $array_error_data[$key] = "<small>訂單(" . $item['data']['create_time'] . "/" . $item['data']['user_id'] . ")：" . $item['error_msg'] . "</small>";
            }
            $this->error('匯入失敗', $url = null, implode("<br>", $array_error_data), $wait = -1);
        }
        $this->success('匯入成功');
    }

    /**
     * 依 Excel 刪除訂單
     */
    public function deleteByImport(Request $request)
    {
        $result = $this->deleteOrderService->deleteByImport($request);

        if (!$result['success']) {
            if (isset($result['errors'])) {
                $this->error($result['message'], null, $result['errors'], -1);
            } else {
                $this->error($result['message']);
            }
        }

        $this->success($result['message']);
    }

    /**
     * 取得儲存格的數值
     */
    private function getNumericCellValue($sheet, $cellName)
    {
        $value = $sheet->getCell($cellName)->getValue();
        return OrderHelper::cleanNumericValue(trim($value));
    }

    private function get_user_id_by_number($number)
    {
        $account = Db::connection('main_db')->table('account')->where('number', $number)->first();
        return $account->id ?? 0;
    }
    private function get_vip_type_reward_by_name($name)
    {
        $vip_type = Db::connection('main_db')->table('vip_type')->where('vip_name', $name)->first();
        return $vip_type->id ?? 0;
    }
    private function get_bonus_model_id_by_name($name)
    {
        $bonus_model = Db::connection('main_db')->table('bonus_model')->where('name', $name)->first();
        return $bonus_model->id ?? 0;
    }

    public function check_stock(Request $request)
    {
        /*更新訂單標記的實體庫存狀態(標記非「超額購買」的訂單標為「足夠」)*/
        OrderHelper::check_stock();
        $this->success(Lang::get('操作成功'));
    }

    public function show(Request $request)
    {
        $id = $request->get('id');
        if (!parent::check_controll($this->tableName, $id, 'main_db')) {
            $this->error('您無法操作此項目');
        }

        $this->data['arr_bonus_models'] = BonusSettingHelper::get_bonus_models([], true)['db_data'];
        $this->data['arr_use_ad'] = BonusSettingHelper::get_use_ad([], true)['db_data'];

        $singleData = DB::connection('main_db')->table($this->tableName)->find($id);
        $singleData = CommonService::objectToArray($singleData);
        $config_db = OrderHelper::get_shop_db_config($singleData['order_number']);

        /*會員資料*/
        $MemberInstance = new MemberInstance($singleData['user_id']);
        $singleData['user'] = $MemberInstance->get_user_data();

        $singleData['receipts_state'] = self::RECEIPTS_STATE[$singleData['receipts_state']];
        $singleData['transport_state'] = self::TRANSPORT_STATE[$singleData['transport_state']];
        $singleData['statusName'] = self::ORDER_STATE[$singleData['status']];

        $product_ids = [-1];
        $distributor_ids = [-1];
        $singleData['product'] = OrderHelper::get_orderform_products([$singleData['id']]);
        foreach ($singleData['product'] as $k => $v) {
            if (isset($v['type_id'])) {
                $singleData['product'][$k]['type_id_ori'] = explode('_', $v['type_id'])[0];
                $singleData['product'][$k]['url'] = str_replace('admin', $this->admin_type, $v['url']);
                array_push($product_ids, $v['info_id']);
                array_push($distributor_ids, $v['distributor_id']);
            }
        }
        // dd($singleData['product']);
        /*檢查是否包含「實體編碼」的商品*/
        $has_instance_product = DB::connection($config_db)->table('productinfo')
            ->where('r_repeat', 0)
            ->whereIn('id', $product_ids)
            ->count();
        $this->data['has_instance_product'] = $has_instance_product;

        $distributor_users = DB::connection('main_db')->table('account')->whereIn('id', $distributor_ids)->pluck('number', 'id');
        // dd($distributor_users);
        $this->data['distributor_users'] = $distributor_users;

        $singleData['discount'] = array_map(function ($value) {
            return get_object_vars($value);
        }, json_decode($singleData['discount']));

        if (empty($singleData['product'][0]['url'])) $singleData['product'][0]['url'] = 'none';

        // 發票類型
        if (isset(config('control.invoice_style_text')[$singleData['InvoiceStyle']])) {
            $singleData['InvoiceStyleText'] = config('control.invoice_style_text')[$singleData['InvoiceStyle']];
        } else {
            $singleData['InvoiceStyleText'] = config('control.invoice_style_text')[1];
        }
        // 發票資料加密
        $singleData['captcha'] = hash('sha256', $singleData['id'] . '-photonic-' . date('Ymd') . 'abZLvDHTqrjCWFgs7e2w');
        // 捐贈碼查詢
        $singleData['LoveCodeText'] = '其他';
        if ($singleData['LoveCode'] != '') {
            $result = DB::table('lovecode')->where('code', $singleData['LoveCode'])->first();
            if (empty($result) == false) {
                $singleData['LoveCodeText'] = $result->name;
            }
        }
        $this->data['singleData'] = $singleData;

        //物流單按鈕
        $transportPaperBtn = "";
        if (config('control.thirdpart_logistic') == 1) {
            if (in_array($singleData['transport'], config('extra.ecpay.shippable'))) {
                if ($singleData['AllPayLogisticsID'] != '') {
                    $Ecpaylogistic = new Ecpaylogistic();
                    $transportPaperBtn = $Ecpaylogistic->checkTransportPaper($id);
                    // dump($transportPaperBtn);exit;
                } else {
                    $transportPaperBtn = '&nbsp;&nbsp;';
                    if ($singleData['transport'] == Lang::get("到店取貨")) { /*當配送方式為「到店取貨」*/
                        $transportPaperBtn = '';
                    } else {
                        $transportPaperBtn_template = '
            <a
              class="btn sendbtn btn-sm text-white mr-3 NoPrint"
              target="_blank"
              href="{send_url}"
              onclick="$(\'#transportPaperBtn_area\').html(\'(已生成物流單，請重新整理)\')"
            >
              {send_name}
            </a>';
                        if ($singleData['transport'] == Lang::get("宅配")) { /*當配送方式為「宅配」*/
                            /*區分「黑貓」、「郵局」*/
                            $url1_btn = str_replace('{send_url}', url('ajax/Ecpaylogistic/createTransportPaper') . '/' . $singleData['id'] . '/TCAT', $transportPaperBtn_template);
                            $url1_btn = str_replace('{send_name}', '黑貓物流單', $url1_btn);
                            $transportPaperBtn .= $url1_btn;
                            $url2_btn = str_replace('{send_url}', url('ajax/Ecpaylogistic/createTransportPaper') . '/' . $singleData['id'] . '/POST', $transportPaperBtn_template);
                            $url2_btn = str_replace('{send_name}', '中華郵政物流單', $url2_btn);
                            $transportPaperBtn .= $url2_btn;
                        } else {
                            $url3_btn = str_replace('{send_url}', url('ajax/Ecpaylogistic/createTransportPaper') . '/' . $singleData['id'], $transportPaperBtn_template);
                            $url3_btn = str_replace('{send_name}', '產生物流單', $url3_btn);
                            $transportPaperBtn .= $url3_btn;
                        }
                    }
                }
            }
        }
        // dump($transportPaperBtn);
        $this->data['transportPaperBtn'] = $transportPaperBtn;

        $pay_fee_dict = Payfee::get_pay_fee_dict();
        $this->data['pay_fee_dict'] = $pay_fee_dict;

        $this->data['NOT_PRODUCTINFO_PRODTYPE'] = Proposal::NOT_PRODUCTINFO_PRODTYPE;

        return view('order.order_ctrl.member-order-info', ['data' => $this->data]);
    }


    /*確認匯款*/
    public function checkReport(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }
        try {
            $this->Order->setReportState();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('更動成功');
    }
    /*更改收款*/
    public function setReceiptsState(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }
        try {
            $state = $request->post('state');
            $this->Order->setReceiptsState($state);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('更動成功');
    }
    /*更改出貨*/
    public function setTransportState(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }
        try {
            $state = $request->post('state');
            $this->Order->setTransportState($state);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('更動成功');
    }
    /*更新訂單備註*/
    public function setPS(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }

        try {
            $ps = $request->post('ps');

            if ($ps) {
                //訂單備註
                $this->Order->setPS($ps);
            } else {
                //訂單備註(消費者查看)
                $ps2 = $request->post('ps2');
                $id = $request->post('id');
                if ($id) {
                    DB::connection('main_db')->table($this->tableName)->where("id", $id)->update(['ps2' => $ps2]);
                }
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('更動成功');
    }
    /*取消*/
    public function changeStatus2Cancel(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }

        try {
            $this->Order->changeStatus2Cancel($request->post('reason'));
        } catch (\Exception $e) {
            $this->dumpException($e);
        }
        $this->success('取消轉換成功');
    }

    public function delete(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }
        $this->Order->delete();
        $this->success('刪除成功');
    }
    /*恢復訂單*/
    public function changeStatus2Restore(Request $request)
    {
        $reason = $request->post();
        $id = $reason['id'];
        if (!parent::check_controll($this->tableName, $id, 'main_db')) {
            $this->error('您無法操作此項目');
        }

        try {
            $this->Order->changeStatus2Restore();
        } catch (\Exception $e) {
            $this->dumpException($e);
        }
        $this->success('恢復轉換成功');
    }

    public function multiCancel(Request $request)
    {
        $idList = $request->post('idList');
        if ($idList) {
            try {
                $idList = json_decode($idList);
                foreach ($idList as $value) {
                    if (!parent::check_controll($this->tableName, $value, 'main_db')) {
                        throw new \Exception('您無法操作此項目', 1);
                    }
                }
                foreach ($idList as $value) {
                    $Order = OrderFactory::createOrder($value, $this->tableName, $this->coupon_tableName);
                    $Order->changeStatus2Cancel('');
                }
            } catch (\Exception $e) {
                $this->dumpException($e);
            }
        }
        if ($idList) {
            $this->success('複數取消成功', url('order/order_ctrl/trash'));
        }

        $this->error('請選擇要修改的項目');
    }
    public function multiNext(Request $request)
    {
        $idList = $request->post('idList');
        if ($idList) {
            try {
                $idList = json_decode($idList);
                foreach ($idList as $value) {
                    if (!parent::check_controll($this->tableName, $value, 'main_db')) {
                        throw new \Exception('您無法操作此項目', 1);
                    }
                }
                foreach ($idList as $value) {
                    $Order = OrderFactory::createOrder($value, $this->tableName, $this->coupon_tableName);
                    $Order->changeStatus2Next();
                }
            } catch (\Exception $e) {
                $this->dumpException($e);
            }
        }
        if ($idList) {
            $this->success('複數轉換成功');
        }
        $this->error('請選擇要修改的項目');
    }
    public function multiDelete(Request $request)
    {
        $idList = $request->post('idList');
        if ($idList) {
            try {
                $idList = json_decode($idList);
                foreach ($idList as $value) {
                    if (!parent::check_controll($this->tableName, $value, 'main_db')) {
                        throw new \Exception('您無法操作此項目', 1);
                    }
                }
                foreach ($idList as $value) {
                    $Order = OrderFactory::createOrder($value, $this->tableName, $this->coupon_tableName);
                    $Order->delete();
                }
            } catch (\Exception $e) {
                $this->dumpException($e);
            }
        }
        if ($idList) {
            $this->success('複數刪除成功', url('order/order_ctrl/trash'));
        }
        $this->error('請選擇要修改的項目');
    }
    public function move_to_pick(Request $request)
    {
        /*POST傳入idList，內容為json格式的訂單id*/
        /*拋轉須排除包含依實體編碼的商品的訂單*/
        /*拋轉須需檢查商品數量是否足夠*/
        /*拋轉成功的商品需標記上「檢貨編號」，請利用 OrderCtrl 的 get_position (可將功能共用化再加以利用)*/
        $id_list = json_decode($request->get('idList'), true);

        $return_data = [
            'success' => [],        /*拋轉成功的訂單，內容為訂單編號(orderform_number)*/
            'out_of_stock' => [],   /*因庫存數量拋轉失敗的訂單，內容為訂單編號(orderform_number)*/
            'instance_code' => [],  /*因品項為實體編碼拋轉失敗的訂單，內容為訂單編號(orderform_number)*/
        ];

        /*更新訂單標記的實體庫存狀態(標記非「超額購買」的訂單標為「足夠」)*/
        OrderHelper::check_stock();

        foreach ($id_list as $id) {
            $orderinfos = DB::connection('main_db')->table($this->tableName)->where('id', $id)->first();
            $orderinfos = CommonService::objectToArray($orderinfos);
            $order_number = $orderinfos['order_number'];
            $products = OrderHelper::get_orderform_products([$orderinfos['id']]);

            /*檢查訂單是否有被標為「實體庫存足夠」*/
            if ($orderinfos['stock_status'] != 1) {
                $return_data['out_of_stock'][] = $order_number;
                continue;
            } else if ($orderinfos['status'] != 'New') {
                $return_data['out_of_stock'][] = $order_number;
                continue;
            }

            /* 檢查訂單是否有包含「依實體編碼」的商品 */
            $flag = 1; /* 預設可以 */
            foreach ($products as $info) {
                if (isset($info['type_id']) == true && isset($info['info_id']) == true) {
                    $productinfo = DB::table('productinfo')->select('r_repeat')->find($info['info_id']);
                    $productinfo = CommonService::objectToArray($productinfo);
                    if ($productinfo['r_repeat'] == '0') {
                        $return_data['instance_code'][] = $order_number;
                        $flag = 0;
                        break;
                    }
                }
            }
            if ($flag == 1) {
                $return_data['success'][] = $order_number;
                DB::connection('main_db')->table($this->tableName)->where('id', $id)->update(['status' => 'Pickable']);
                $this->write_history($orderinfos);
            }
        }

        return $return_data;
    }

    /*AJAX 物流狀態*/
    public function ajax_logistic_status(Request $request)
    {
        $id = $request->get('id');
        if (!parent::check_controll($this->tableName, $id, 'main_db')) {
            return [];
        }
        $singleData = DB::connection('main_db')->table($this->tableName)->where('id', $id)->first();

        $order = DB::connection('main_db')->table($this->tableName)->find($id);
        $order = CommonService::objectToArray($order);
        $AllPayLogisticsID = $order['AllPayLogisticsID'];

        $logisticsRecord = DB::connection('main_db')->table('logistics_record as lr')
            ->distinct()
            ->select('lr.time', 'lc.type', 'lc.message', 'lr.id')
            ->leftJoin('logistics_code as lc', function ($query) {
                $query->on('lr.RtnCode', '=', 'lc.code')
                    ->on('lr.LogisticsType', '=', 'lc.type');
            })
            ->where('lr.order_id', $id);
        if (empty($AllPayLogisticsID) == false) {
            $logisticsRecord = $logisticsRecord->where('lr.logistics_id', $AllPayLogisticsID);
        }
        $logisticsRecord = $logisticsRecord->orderBy('lr.id', 'asc')->get();
        $logisticsRecord = CommonService::objectToArray($logisticsRecord);
        return $logisticsRecord;
    }

    /* 取得商品品項庫存編碼 */
    public function get_position(Request $request)
    {
        if (!empty($request->post('order_number'))) {
            $order_number = $request->post('order_number');
        } else {
            $this->error('請提供訂單編號');
        }

        if (!empty($request->post('prod_id'))) {
            $prod_id = $request->post('prod_id');
        } else {
            $this->error('請提供商品ID');
        }

        if (!empty($request->post('type_id'))) {
            $type_id = $request->post('type_id');
        } else {
            $this->error('請提供品項ID');
        }

        $orderdata = DB::connection('main_db')->table($this->tableName)->whereRaw('order_number="' . $order_number . '"')->first();
        if (empty($orderdata) == true) {
            $this->error('無此項目');
        }
        if (!parent::check_controll($this->tableName, $orderdata->id, 'main_db')) {
            $this->error('您無法操作此項目');
        }

        // 處理type_id
        [$key_id, $key_type] = Proposal::get_prod_key_type($type_id);
        if (in_array($key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) {
            $this->error('此商品無須揀貨');
        }
        $config_db = OrderHelper::get_shop_db_config($order_number);

        $productinfo = DB::table('productinfo')->where('id', $prod_id)->get();
        $productinfo = CommonService::objectToArray($productinfo);
        if (empty($productinfo) == true) {
            $this->error('查無商品');
        }

        if (!parent::check_controll('productinfo', $prod_id, $config_db)) {
            $this->error('您無法操作此項目');
        }

        $position_portion = DB::connection($config_db)->table('position_portion as pp')
            ->select(
                'pos.name as p_name',
                'pos.number as p_number',
                'pos.max as p_max',
                'pp.position_id',
                'pp.position_number',
                'pp.id as pp_id',
                'pp.num'
            )
            ->whereRaw("pp.productinfo_type = '" . $key_id . "'")
            ->leftJoin('position as pos', 'pos.id', '=', 'pp.position_id')
            ->orderByRaw('pp.position_number asc')
            ->get();

        $position_portion = CommonService::objectToArray($position_portion);

        array_walk($position_portion, function ($item, $key) use (&$position_portion) {
            $position_portion[$key]['p_code'] = $item['p_max'] == "1" ? $item['p_name'] . $item['position_number'] : $item['p_name'] . str_pad($item['position_number'], strlen($item['p_number']), "0", STR_PAD_LEFT);
        });

        if ($productinfo[0]['r_repeat'] == 0) { // 依實體編碼
            return ['code' => 1, 'position_portion' => $position_portion];
        } else {  // 依品項編碼
            $orderform = DB::connection('main_db')->table($this->tableName)->where('order_number', $order_number)->get();
            $orderform = CommonService::objectToArray($orderform);
            if (empty($orderform) == true) $this->error('查無此訂單');

            $product = OrderHelper::get_orderform_products([$orderform[0]['id']]);
            // dd($product);
            foreach ($product as $k => $v) {
                if (!isset($v['type_id'])) {
                    continue;
                }
                if (!$v['type_id']) {
                    continue;
                }

                if ($v['type_id'] == $key_id && $v['key_type'] == $key_type) {
                    DB::table('position_portion')->whereRaw('product_id=' . $prod_id . ' AND productinfo_type=' . $key_id)->limit(1)->decrement('num', $v['num']); // 釋出庫存編碼、扣數量
                    DB::table('position_portion')->where('num', 0)->delete(); // 編碼剩餘數為0則刪除紀錄
                    DB::table('position_portion')->where('product_id', 0)->delete(); // 編碼無對應商品則刪除紀錄

                    $config_db = OrderHelper::get_shop_db_config($orderform[0]['order_number']);
                    Db::connection($config_db)->table('picked_history')
                        ->insert([
                            'order_id' => $orderform[0]['id'],
                            'order_time' => $orderform[0]['create_time'],
                            'p_code' => $position_portion[0]['p_code'],
                            'num' => $v['num'],
                            'product_id' => $prod_id,
                            'productinfo_type' => $key_id,
                            'position_id' => $position_portion[0]['position_id'],
                            'position_number' => $position_portion[0]['position_number'],
                            'deal_position' => 1,
                            'datetime' => date('Y-m-d H:i:s'),
                        ]);
                    DB::connection('main_db')->table('orderform_product')
                        ->where('orderform_id', $orderform[0]['id'])
                        ->where('type_id', $key_id)
                        ->where('key_type', $key_type)
                        ->update([
                            'deal_position' => 1,
                            'position_code' => $position_portion[0]['p_code'] . ":" . $v['num'] . '個',
                        ]);
                    $product[$k]['deal_position'] = 1;
                    $product[$k]['position_code'] = $position_portion[0]['p_code'] . ":" . $v['num'] . '個';
                    break;
                }
            }

            // 更新訂單狀態
            $order_update = ['product' => json_encode($product, JSON_UNESCAPED_UNICODE),];
            // 檢查是否有未撿貨的商品
            $has_not_picked = DB::connection('main_db')->table('orderform_product')
                ->where('orderform_id', $orderform[0]['id'])
                ->whereNotNull('key_type')
                ->where('deal_position', 0)
                ->count();
            if ($has_not_picked == 0) { // 無未撿貨商品
                $order_update['status'] = 'Picked'; // 更新訂單為可寄出
            }
            DB::connection('main_db')->table($this->tableName)->whereRaw('order_number="' . $order_number . '"')->update($order_update);

            return ['code' => 2];
        }
    }
    /* 釋出庫存編碼、扣數量 */
    public function release_position(Request $request)
    {
        if (!empty($request->post('order_number'))) {
            $order_number = $request->post('order_number');
        } else {
            $this->error('請提供訂單編號');
        }

        if (!empty($request->post('type_id'))) {
            $type_id = $request->post('type_id');
        } else {
            $this->error('請提供品項ID');
        }

        if (!empty($request->post('positions'))) {
            $positions = $request->post('positions');
        } else {
            $this->error('請提供庫存編碼ID');
        }

        $orderdata = DB::connection('main_db')->table($this->tableName)->whereRaw('order_number="' . $order_number . '"')->first();
        $orderdata = CommonService::objectToArray($orderdata);
        if (empty($orderdata) == true) {
            $this->error('無此項目');
        }
        if (!parent::check_controll($this->tableName, $orderdata['id'], 'main_db')) {
            $this->error('您無法操作此項目');
        }

        // 處理type_id
        [$key_id, $key_type] = Proposal::get_prod_key_type($type_id);
        if (in_array($key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) {
            $this->error('此商品無須揀貨');
        }
        $config_db = OrderHelper::get_shop_db_config($order_number);

        $productinfo_type = DB::connection($config_db)->table('productinfo_type')->where('id', $type_id)->first();
        if (empty($productinfo_type) == true) {
            $this->error('無此項目');
        }
        if (!parent::check_controll('productinfo', $productinfo_type->product_id, $config_db)) {
            $this->error('您無法操作此項目');
        }

        $position_code = [];
        $release_time = date('Y-m-d H:i:s');
        foreach ($positions as $k => $v) {
            DB::connection($config_db)->table('position_portion')->whereRaw('id=' . $v['pp_id'])->decrement('num', $v['num']); // 釋出庫存編碼、扣數量

            /*建立揀貨資料(依實體編碼)*/
            $r = Db::connection($config_db)->table('position_portion')->whereRaw('id=' . $v['pp_id'])->first();
            $r = CommonService::objectToArray($r);
            Db::connection($config_db)->table('picked_history')
                ->insert([
                    'order_id' => $orderdata['id'],
                    'order_time' => $orderdata['create_time'],
                    'p_code' => $v['p_code'],
                    'num' => $v['num'],
                    'product_id' => $r['product_id'],
                    'productinfo_type' => $r['productinfo_type'],
                    'position_id' => $r['position_id'],
                    'position_number' => $r['position_number'],
                    'deal_position' => 1,
                    'datetime' => $release_time,
                ]);
            array_push($position_code, $v['p_code'] . ":" . $v['num'] . '個');
        }
        DB::connection($config_db)->table('position_portion')->whereRaw('num<=0')->delete(); // 編碼剩餘數為0(貨更小)則刪除紀錄
        DB::connection($config_db)->table('position_portion')->where('product_id', 0)->delete(); // 編碼無對應商品則刪除紀錄

        $product = OrderHelper::get_orderform_products([$orderdata['id']]);
        foreach ($product as $k => $v) {
            if (!isset($v['type_id'])) {
                continue;
            }
            if (!$v['type_id']) {
                continue;
            }
            if ($v['type_id'] == $key_id && $v['key_type'] == $key_type) {
                $product[$k]['deal_position'] = 1;
                $product[$k]['position_code'] = join(',', $position_code);
                DB::connection('main_db')->table('orderform_product')
                    ->where('orderform_id', $orderdata['id'])
                    ->where('type_id', $key_id)
                    ->where('key_type', $key_type)
                    ->update([
                        'deal_position' => 1,
                        'position_code' => join(',', $position_code),
                    ]);
            }
        }

        // 更新訂單狀態
        $order_update = ['product' => json_encode($product, JSON_UNESCAPED_UNICODE),];
        // 檢查是否有未撿貨的商品
        $has_not_picked = DB::connection('main_db')->table('orderform_product')
            ->where('orderform_id', $orderdata['id'])
            ->whereNotNull('key_type')
            ->where('deal_position', 0)
            ->count();
        if ($has_not_picked == 0) { // 無未撿貨商品
            $order_update['status'] = 'Picked'; // 更新訂單為可寄出
        }
        DB::connection('main_db')->table($this->tableName)->whereRaw('order_number="' . $order_number . '"')->update($order_update);

        $this->success('揀貨成功');
    }

    /*修改訂單內容*/
    public function update(Request $request)
    {
        // dd($request->post());
        if (!$request->post()) {
            $this->error('請提供更新內容');
        }
        if (empty($request->post('id'))) {
            $this->error('請提供更新訂單');
        }
        $id = $request->post('id');
        if (!parent::check_controll($this->tableName, $id, 'main_db')) {
            $this->error('您無法操作此項目');
        }

        $user_collection = Db::connection('main_db')->table('account')->get();
        $db_id_to_user = $user_collection->keyBy('id')->toArray();

        $data = [];
        $allow_change = [
            'transport_location',
            'transport_location_name',
            'transport_location_phone',
            'GoodsWeight',
            'InvoiceNo',
            'InvoiceDate',
            'user_id_operation',
            'user_id_lecturer',
            'user_id_center',
        ];
        foreach ($allow_change as $key) {
            if (!is_null($request->post($key))) {
                $data[$key] = $request->post($key);
                if ($key == 'InvoiceDate') {
                    $data[$key] = date('Y-m-d', strtotime($data[$key]));
                } else if ($key == 'user_id_operation') {
                    if (!isset($db_id_to_user[$data[$key]])) {
                        $this->error('營運者會員ID設定有誤，無此會員');
                    }
                } else if ($key == 'user_id_lecturer') {
                    if (!isset($db_id_to_user[$data[$key]])) {
                        $this->error('講師會員ID設定有誤，無此會員');
                    }
                } else if ($key == 'user_id_center') {
                    if (!isset($db_id_to_user[$data[$key]])) {
                        $this->error('中心會員ID設定有誤，無此會員');
                    } else if ($db_id_to_user[$data[$key]]->id == 10) {
                        /*略過中心檢查*/
                    } else if ($db_id_to_user[$data[$key]]->center_level_id == 0) {
                        $this->error('中心會員ID設定有誤，此會員非有效中心');
                    }
                }
            }
        }
        // dump($data);exit;
        DB::connection('main_db')->table($this->tableName)->whereRaw('id="' . $id . '"')->update($data);
        $this->success('修改成功');
    }

    /*待取庫存 - 確認*/
    public function move_to_picked(Request $request)
    {
        $input = $request->post();

        $pick_num = $input['pick_num'];

        $result = DB::table('picked_history')->select('*')
            ->where('deal_position', 0)
            ->orderBy('order_time', 'asc');
        if (empty(config('control.close_function_current')['存放位置管理'])) {
            $result = $result->where('p_code', $input['position_code']);
        } else {
            $result = $result->where('productinfo_type', $input['position_code']);
        }
        $result = $result->get();

        $pick_time = date('Y-m-d H:i:s');
        if (empty($result) == false) {
            $result = CommonService::objectToArray($result);
            foreach ($result as $info) {
                if ($pick_num == 0) {
                    break;
                }

                if ($info['num'] <= $pick_num) {
                    $pick_num -= $info['num'];
                    DB::table('picked_history')->whereRaw("id = '{$info['id']}'")->update(['deal_position' => 1, 'datetime' => $pick_time]);
                } else {
                    if (empty(config('control.close_function_current')['庫存警示']) == true) {
                        DB::table('picked_history')->whereRaw("id = '{$info['id']}'")->update(['num' => $info['num'] - $pick_num]);
                    }

                    DB::table('picked_history')->insert([
                        'order_id' => $info['order_id'],
                        'order_time' => $info['order_time'],
                        'p_code' => $info['p_code'],
                        'num' => $pick_num,
                        'product_id' => $info['product_id'],
                        'productinfo_type' => $info['productinfo_type'],
                        'position_id' => $info['position_id'],
                        'position_number' => $info['position_number'],
                        'deal_position' => 1,
                        'datetime' => $pick_time,
                    ]);
                    break;
                }
            }

            $this->check_orders(['position_id' => $info['position_id'], 'position_number' => $info['position_number']]);
        }
    }

    // 列印訂單
    public function print_order(Request $request)
    {
        $id = $request->get('id');
        $singleData = DB::connection('main_db')->table($this->tableName)->find($id);
        $singleData = CommonService::objectToArray($singleData);

        $singleData['subtotal'] = 0;
        $singleData['product'] = $product = OrderHelper::get_orderform_products([$singleData['id']]);
        foreach ($singleData['product'] as $k => $arr) {
            if (is_null($arr['type_id'])) {
                $singleData['shipping_fee'] = $arr['total'];
                unset($singleData['product'][$k]);
            } else {
                $singleData['subtotal'] += $arr['total'];
            }
        }

        // $singleData['discount']: [{"type":"使用紅利點數","name":"數量106","count":"扣$106","dis":106}]
        $discount_arr = json_decode($singleData['discount'], true);
        $discount_total = 0;
        foreach ($discount_arr as $k => $v) {
            $discount_total += $v['dis'];
        }
        $singleData['discount'] = $discount_total;

        $payment_map = [
            '1' => '貨到付款',
            '2' => 'ATM轉帳\\匯款',
            '3' => '線上刷卡',
            '4' => '分期付款',
            '5' => 'LinePay',
        ];
        $singleData['payment'] = $payment_map[$singleData['payment']];

        if ($singleData['ps2'] == '') {
            $singleData['ps2'] = '無';
        }

        $singleData['company_name'] = config('extra.shop.company_name');
        $singleData['service_tel'] = config('extra.shop.service_tel');
        $singleData['service_email'] = config('extra.shop.service_email');
        $singleData['service_address'] = config('extra.shop.service_address');

        $singleData['icon'] = DB::table('index_excel')->orderBy('id')->first()->data1;

        $this->data['singleData'] = $singleData;

        return view('order.order_ctrl.print_order', ['data' => $this->data]);
    }

    private function write_history($orderinfos)
    {
        $order_id = $orderinfos['id'];
        $config_db = OrderHelper::get_shop_db_config($orderinfos['order_number']);
        $product = OrderHelper::get_orderform_products([$orderinfos['id']]);
        foreach ($product as $k => $v) {
            if (!isset($v['type_id'])) {
                continue;
            }
            if (!$v['type_id']) {
                continue;
            }

            $num = $v['num'];
            $type_id = $v['type_id'];
            $product_id = $v['info_id'];
            $key_type = $v['key_type'];
            if (in_array($key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) {
                $this->error('此商品無須揀貨');
            }

            $productinfo = DB::connection($config_db)->table('productinfo')->whereRaw("id = '$product_id'")->get();
            if (empty($productinfo) == true) {
                $this->error('查無商品');
            }

            if (!parent::check_controll('productinfo', $product_id, $config_db)) {
                $this->error('您無法操作此項目');
            }

            if (isset(config('control.close_function_current')['存放位置管理']) == true) {
                DB::connection($config_db)->table('picked_history')
                    ->insert([
                        'order_id' => $order_id,
                        'order_time' => $orderinfos['create_time'],
                        'num' => $num,
                        'product_id' => $product_id,
                        'productinfo_type' => $type_id,
                        'deal_position' => 0,
                    ]);
            } else {
                $position_portion = DB::connection($config_db)->table('position_portion as pp')
                    ->select(
                        'pos.id as p_id',
                        'pos.name as p_name',
                        'pos.number as  p_number',
                        'pos.max as p_max',
                        'pp.position_number',
                        'pp.id as pp_id'
                    )
                    ->whereRaw("pp.productinfo_type = '" . $type_id . "'")
                    ->leftJoin('position as pos', 'pos.id', '=', 'pp.position_id')
                    ->orderByRaw('pp.position_number asc')
                    ->get();
                $position_portion = CommonService::objectToArray($position_portion);
                array_walk($position_portion, function ($item, $key) use (&$position_portion) {
                    $position_portion[$key]['p_code'] = $item['p_max'] == "1" ? $item['p_name'] . $item['position_number'] : $item['p_name'] . str_pad($item['position_number'], strlen($item['p_number']), "0", STR_PAD_LEFT);
                });

                DB::connection($config_db)->table('picked_history')->insert([
                    'order_id' => $order_id,
                    'order_time' => $orderinfos['create_time'],
                    'p_code' => $position_portion[0]['p_code'],
                    'num' => $num,
                    'product_id' => $product_id,
                    'productinfo_type' => $type_id,
                    'position_id' => $position_portion[0]['p_id'],
                    'position_number' => $position_portion[0]['position_number'],
                    'deal_position' => 0,
                ]);
            }
        }
    }

    /*檢查訂單*/
    private function check_orders($check_info)
    {
        $orderforms = DB::connection('main_db')->table($this->tableName)->whereRaw('status = "Pickable"')->get();

        if (empty($orderforms) == false) {
            $orderforms = CommonService::objectToArray($orderforms);
            foreach ($orderforms as $orderform) {
                $order_id = $orderform['id'];
                $config_db = OrderHelper::get_shop_db_config($orderform['order_number']);
                $products = OrderHelper::get_orderform_products([$order_id]);

                $product_amount = count($products);
                $cc = 1;
                foreach ($products as $product) {
                    if (isset($product['type_id']) == true) {
                        $product_id = $product['info_id'];
                        $productinfotype = $product['type_id'];
                        $num = $product['num'];

                        if (isset(config('control.close_function_current')['存放位置管理']) == true) {
                            $history = DB::table('picked_history as ph')
                                ->selectRaw('SUM(ph.num) as count, ph.product_id, ph.productinfo_type')
                                ->whereRaw("ph.order_id = '{$order_id}' AND ph.product_id = '{$product_id}' AND ph.productinfo_type = '{$productinfotype}' AND ph.deal_position = '1'")
                                ->first();
                            $history = CommonService::objectToArray($history);
                            if (empty($history) == true) {
                                $this->error('揀貨列表無此項目');
                            }
                            if ($history['count'] == $num) {
                                $product['deal_position'] = 1;
                                $cc += 1;
                            }
                        } else {
                            $position_name = $product['position'];

                            $history = DB::table('picked_history as ph')
                                ->selectRaw('SUM(ph.num) as count, ph.product_id, ph.productinfo_type, ph.p_code, ph.position_id, ph.position_number, p.max as p_max')
                                ->leftJoin('position as p', 'p.id', '=', 'ph.position_id')
                                ->whereRaw("ph.order_id = '{$order_id}' AND p.name = '{$position_name}' AND ph.product_id = '{$product_id}' AND ph.productinfo_type = '{$productinfotype}' AND ph.deal_position = '1'")
                                ->first();
                            $history = CommonService::objectToArray($history);
                            if (empty($history) == true) {
                                $this->error('揀貨列表無此項目');
                            }
                            if ($history['count'] == $num) {
                                $product['deal_position'] = 1;
                                $product['position_code'] = $history['p_code'] . ':' . $num . '個';
                                $cc += 1;

                                if ($check_info['position_id'] == $history['position_id'] && $check_info['position_number'] == $history['position_number']) {
                                    DB::connection($config_db)->table('position_portion')->whereRaw("product_id = '{$history['product_id']}' AND productinfo_type = '{$history['productinfo_type']}'")->limit(1)->decrement('num', $num); // 釋出庫存編碼、扣數量
                                }
                                DB::connection($config_db)->table('position_portion')->where('num', 0)->delete(); // 編碼剩餘數為0則刪除紀錄
                                DB::connection($config_db)->table('position_portion')->where('product_id', 0)->delete(); // 編碼無對應商品則刪除紀錄

                                DB::connection('main_db')->table('orderform_product')
                                    ->where('orderform_id', $product['orderform_id'])
                                    ->where('type_id', $product['type_id'])
                                    ->update([
                                        'deal_position' => 1,
                                        'position_code' => $history['p_code'] . ':' . $num . '個',
                                    ]);
                            }
                        }
                    }
                }

                if ($product_amount == $cc) {
                    DB::connection('main_db')->table($this->tableName)->where('id', $order_id)->update([
                        'product' => json_encode($products, JSON_UNESCAPED_UNICODE),
                        'status' => 'Picked',
                    ]);
                }
            }
        }
    }

    /*處理訂單回饋*/
    public function do_pointback(Request $request)
    {
        // $this->error('功能調整中，暫不開放');

        $orderfomr_id = $request->get('id');
        // dd($orderfomr_id);
        try {
            OrderHelper::do_pointback([$orderfomr_id]);
        } catch (\Throwable $th) {
            // throw $th;
            $this->error($th->getMessage());
        }
        $this->success('操作成功');
    }

    /*取消訂單回饋(Rollback)*/
    public function rollbackPointback(Request $request)
    {
        $orderformId = $request->get('id');

        if (!$orderformId) {
            $this->error('請提供訂單ID');
        }

        try {
            $results = $this->rollbackService->rollbackPointback([$orderformId]);
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success('操作成功');
    }
}
