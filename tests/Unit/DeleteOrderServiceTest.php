<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Order\DeleteOrderService;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DeleteOrderServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $deleteOrderService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->deleteOrderService = new DeleteOrderService();
    }

    /** @test */
    public function it_can_delete_orders_by_import_data()
    {
        // 準備測試資料
        DB::connection('main_db')->table('account')->insert([
            'id' => 1,
            'number' => 'TEST001',
            'name' => 'Test User'
        ]);

        DB::connection('main_db')->table('orderform')->insert([
            'id' => 1,
            'user_id' => 1,
            'create_time' => strtotime('2024-01-01'),
            'transport_location_name' => '<PERSON>',
            'transport_location_phone' => '**********',
            'transport_location' => 'Test Address',
            'transport_email' => '<EMAIL>',
            'total' => 1000
        ]);

        $orderData = [
            [
                'create_time' => '2024-01-01',
                'user_id' => 'TEST001',
                'transport_location_name' => 'John Doe',
                'transport_location_phone' => '**********',
                'transport_location' => 'Test Address',
                'transport_email' => '<EMAIL>',
                'total' => 1000
            ]
        ];

        // 執行刪除
        $result = $this->deleteOrderService->deleteOrdersByImport($orderData);

        // 驗證結果
        $this->assertEmpty($result); // 沒有錯誤
        $this->assertDatabaseMissing('orderform', ['id' => 1]);
    }

    /** @test */
    public function it_returns_error_when_user_not_found()
    {
        $orderData = [
            [
                'create_time' => '2024-01-01',
                'user_id' => 'NONEXISTENT',
                'transport_location_name' => 'John Doe'
            ]
        ];

        $result = $this->deleteOrderService->deleteOrdersByImport($orderData);

        $this->assertCount(1, $result);
        $this->assertEquals('無此消費者', $result[0]['error_msg']);
    }

    /** @test */
    public function it_returns_error_when_create_time_is_missing()
    {
        $orderData = [
            [
                'create_time' => '',
                'user_id' => 'TEST001'
            ]
        ];

        $result = $this->deleteOrderService->deleteOrdersByImport($orderData);

        $this->assertCount(1, $result);
        $this->assertEquals('未設定訂單時間', $result[0]['error_msg']);
    }

    /** @test */
    public function it_handles_file_upload_validation()
    {
        $request = new Request();
        
        // 測試沒有檔案的情況
        $result = $this->deleteOrderService->deleteByImport($request);
        
        $this->assertFalse($result['success']);
        $this->assertEquals('請上傳Excel檔', $result['message']);
    }
}
